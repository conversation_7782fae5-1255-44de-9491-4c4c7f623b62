package com.ejuetc.saasapi.sdk.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;

import java.security.NoSuchAlgorithmException;
import java.util.Map;

import static java.util.UUID.randomUUID;

@Slf4j
@SpringBootTest
public class SDKTest {
//        SaaSApiSDK sdk = new SaaSApiSDK(
//                "c90982b1474641c5b9ad10724f4f8cd4",
//                "gcuTerMoadFj8nQkFP7U3i84BZu5shoDBO0xkxB3ehI=",
//                "http://saasapi-test.ebaas.com/gateway/invoke"
//        );
        SaaSApiSDK sdk = new SaaSApiSDK(
                "cd0badf8c664484a9500e3f1a5351623",
                "RDX6D3bPTwIReEmi06D0PJz9VJ2WthhTmj2WYs7fXdI=",
                "http://saasapi-test.ebaas.com/gateway/invoke"
        );

    @Test
    public void queryHasDetailByName() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryHasDetailByName",
                randomUUID().toString(),
                """
                        {
                        	"pageSize":5,
                        	"cityId":310100,
                        	"keyword":"慧芝湖",
                        	"pageNum":1
                        }""");
        System.out.println(resp);
    }

    @Test
    public void queryLayoutGroup() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryLayoutGroup",
                randomUUID().toString(),
                """
                        {
                         	"address":"上海",
                         	"name":"慧芝湖"
                         }""");
        System.out.println(resp);
    }

    @Test
    public void queryDetail() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryDetail",
                randomUUID().toString(),
                """
                        {
                         	"address":"上海",
                         	"name":"慧芝湖"
                         }""");
        System.out.println(resp);
    }

    @Test
    public void testFeignClient() {
        // 这是一个示例测试，展示如何使用 feignClient 功能
        // 实际使用时需要定义具体的 API 接口
        log.info("FeignClient 功能已集成，可以通过 sdk.feignClient(ApiInterface.class, \"http://target-service-url\") 来使用");
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        String sign = SaaSApiSDK.calcSign(Map.of("etc-b", "2", "etc-a", "1"), "{}", "TN1WU0ggSqBBUPp7ZlhO6rVH8MoUQW/WFCp7LUyp0Gs=");
        System.out.println(sign);

    }
}