package com.ejuetc.saasapi.apiImpl;

import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.saasapi.adapter.GatewayAdapter;
import com.ejuetc.saasapi.domain.gateway.invoke.Headers;
import com.ejuetc.saasapi.domain.gateway.invoke.Invoke;
import com.ejuetc.saasapi.domain.gateway.invoke.InvokeRpt;
import com.ejuetc.saasapi.pro.GetawayResponse;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@Slf4j
@RestController
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class InvoiceImpl implements GatewayAdapter<String> {

    private final InvokeRpt invokeRpt;
    private final EntityManager entityManager;

    @PostMapping("/gateway/invoke")
    public Object invoke(
            @RequestHeader Map<String, String> headersMap,
            @RequestParam Map<String, String[]> params,
            @RequestBody(required = false) String body,
            @RequestParam(value = "file", required = false) MultipartFile file) {
        Headers headers = new Headers(headersMap);
        Invoke invoke = invokeRpt.findExist(headers).orElseGet(() ->
                getBean(InvoiceImpl.class).createInvoke(headers, params, body, file).merge()
        );
        return invoke.exec(headers, body, file);
    }

    @Transactional(propagation = REQUIRES_NEW)
    Invoke createInvoke(Headers headers, Map<String, String[]> params, String body, MultipartFile file) {
        return new Invoke(headers, params, body, file).save();
    }

    @Override
    @PostMapping("/mock/api")
    public GetawayResponse gatewayApi(@RequestHeader Map<String, String> headers, @RequestBody String requestBody) {
        return new GetawayResponse(
                ResponseStatus.SUCC_DONE,
                "成功",
                Map.of(
                        "responseKey1", "value1",
                        "responseKey2", "value2"
                )
        );
    }

}