package com.ejuetc.saasapi.domain.gateway.api;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.saasapi.domain.gateway.invoke.Invoke;
import com.ejuetc.saasapi.pro.GetawayResponse;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("网关对外提供的API接口")
@Table(name = "tb_gateway_api")
@Where(clause = "logic_delete = 0")
//@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('FORWARD_REQUEST','UPLOAD_FILE','FEIGN_CLIENT') COMMENT '接口类型'")
public abstract class Api extends BaseEntity<Api> implements Serializable {

    @Id
    @GeneratedValue(generator = "gateway_api_id")
    @SequenceGenerator(name = "gateway_api_id", sequenceName = "seq_gateway_api_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT 'API代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '名称'")
    private String name;

    @Column(name = "idempotent", nullable = false, columnDefinition = "bit(1) DEFAULT b'1' COMMENT '接口是否支持幂等'")
    protected Boolean idempotent = true;


    public GetawayResponse invoke(Invoke invoke, MultipartFile file){
        try{
            return doInvoke(invoke, file);
        }catch (Throwable t){
            log.error("invoke error", t);
            return new GetawayResponse(t);
        }
    }

    public abstract GetawayResponse doInvoke(Invoke invoke, MultipartFile file);

}
